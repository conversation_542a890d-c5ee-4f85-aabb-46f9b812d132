# 连续上涨且涨幅达到一定程度就入场做空
# 资金费率大于-0.01，且4h涨幅大于15%

import logging
import pandas as pd
import talib.abstract as ta
from typing import Optional,Tuple
from freqtrade.persistence import Trade
from datetime import datetime, timedelta
from freqtrade.strategy import informative
from freqtrade.strategy.interface import IStrategy

logger = logging.getLogger(__name__)
# 这个版本主要用于开发

class Short_Up_Funding_1H(IStrategy):
    INTERFACE_VERSION = 3               # freqtrade策略接口版本号
    timeframe = '1h'                    # 策略运行的时间周期
    startup_candle_count = 24           # 策略启动需要的历史K线数量
    minimal_roi = {"0": 100}            # ROI：设置为100%意味着禁用传统ROI
    MAX_LEVERAGE = 10.0                 # 最大杠杆倍数
    max_open_trades = 20                # 最大同时持仓数量
    stoploss = -1                       # 固定止损设置
    trailing_stop = False               # 禁用跟踪止损
    trailing_stop_positive = None       # 禁用跟踪止损的盈利触发点
    use_custom_stoploss = True          # 使用自定义止损
    stoploss_on_exchange = True         # 在交易所层面执行自定义止损
    use_exit_signal = True              # 是否使用退出信号
    exit_profit_only = False            # 是否只在盈利时退出
    can_short = True                    # 是否允许做空交易
    ignore_roi_if_entry_signal = True   # 是否在有入场信号时忽略ROI
    position_adjustment_enable = True   # 是否启用仓位调整
    process_only_new_candles = True     # 是否只在新K线形成时处理
    
    def __init__(self, config: dict) -> None:
        super().__init__(config)

        self.total_stake = 1000          # 总仓位基数
        
    def informative_pairs(self):
        """定义需要获取的交易对信息"""
        pairs = self.dp.current_whitelist()     # 获取当前白名单中的所有交易对，为主时间周期添加所有交易对，这是必需的
        informative_pairs = []                  # 用于存储需要获取的交易对信息
        informative_pairs.extend([(pair, self.timeframe) for pair in pairs])        # 为主时间周期添加所有交易对
        informative_pairs.extend([(pair, '1d') for pair in pairs])                  # 为1d时间周期添加所有交易对
        active_trades = Trade.get_open_trades()
        active_pairs = set(trade.pair for trade in active_trades)
        if active_pairs:
            informative_pairs.extend([(pair, '1m') for pair in active_pairs])       # 为活跃交易对添加1分钟K线数据
            informative_pairs.extend([(pair, '3m') for pair in active_pairs])       # 为活跃交易对添加3分钟K线数据
            informative_pairs.extend([(pair, '5m') for pair in active_pairs])       # 为活跃交易对添加5分钟K线数据
            informative_pairs.extend([(pair, '15m') for pair in active_pairs])      # 为活跃交易对添加15分钟K线数据
        informative_pairs.extend([(pair, '8h', 'funding_rate') for pair in pairs])  # 添加资金费率数据（8h，funding_rate candle_type）
        return informative_pairs

    @informative('8h', candle_type='funding_rate')
    def populate_funding_rate(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        dataframe['funding_rate'] = dataframe['open'].round(10)          # 资金费率一般在 open 列
        return dataframe
    
    @informative('1d')
    def populate_indicators_1d(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """计算日线级别的指标"""
        dataframe['consecutive_day'] = dataframe['close'] <= dataframe['open']          # 标记下跌日
        dataframe['consecutive_day_count'] = dataframe['consecutive_day'].cumsum() - dataframe['consecutive_day'].cumsum().where(~dataframe['consecutive_day']).ffill().fillna(0)
        dataframe['increase_day'] = dataframe['close'].pct_change(1) * 100              # 1天的K线的涨幅
        dataframe['increase_day7'] = dataframe['increase_day'].rolling(7).sum()         # 7天的K线的涨幅
        dataframe['increase_day14'] = dataframe['increase_day'].rolling(14).sum()       # 14天的K线的涨幅
        return dataframe

    def populate_indicators(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """调用市场指标和单个交易对技术指标"""
        
        # 计算单个交易对技术指标
        dataframe = dataframe.sort_values('date', ascending=True)                       # 确保数据按时间排序
        dataframe['increase_1h'] = dataframe['close'].pct_change(1) * 100               # 1小时K线的涨幅
        dataframe['increase_3h'] = dataframe['increase_1h'].rolling(3).sum()            # 3小时K线的涨幅
        dataframe['increase_4h'] = dataframe['increase_1h'].rolling(4).sum()            # 4小时K线的涨幅
        dataframe['increase_6h'] = dataframe['increase_1h'].rolling(6).sum()            # 6小时K线的涨幅
        dataframe['increase_12h'] = dataframe['increase_1h'].rolling(12).sum()          # 12小时K线的涨幅
        dataframe['increase_24h'] = dataframe['increase_1h'].rolling(24).sum()          # 24小时K线的涨幅
        dataframe['k1_increase'] = dataframe['increase_1h'].shift(0)                    # 当前时间的前一根k线涨幅
        dataframe['volume_$'] = dataframe['volume'] * dataframe['close']                # 成交量乘以收盘价
        dataframe['volume_24h'] = dataframe['volume_$'].rolling(24).sum()               # 24小时成交量
        
        # 计算技术指标
        dataframe['ema_25'] = ta.EMA(dataframe['close'], timeperiod=25)
        dataframe['ema_50'] = ta.EMA(dataframe['close'], timeperiod=50)
        dataframe['ema_100'] = ta.EMA(dataframe['close'], timeperiod=100)
        
        # 计算连续上涨K线数量 - 基于1小时K线
        dataframe['consecutive_hour'] = dataframe['close'] > dataframe['open']
        dataframe['consecutive_hour_count'] = dataframe['consecutive_hour'].cumsum() - dataframe['consecutive_hour'].cumsum().where(~dataframe['consecutive_hour']).ffill().fillna(0)
        
        return dataframe

    def populate_entry_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        # 初始化入场信号列
        dataframe['enter_short'] = 0
        dataframe['enter_tag'] = None

        dataframe.loc[
            (dataframe['funding_rate_8h'] >= -0.01) &
            (dataframe['increase_day7_1d'] >= 300) &
            (dataframe['increase_6h'] >= 15) &
            (dataframe['avg_increase_4h'] <= 3) &
            (dataframe['volume_24h'] > 10000000),
            ['enter_short', 'enter_tag']
        ] = [1, 'SELL_FALL_1']
        return dataframe

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float,
                          time_in_force: str, current_time: datetime, entry_tag: Optional[str],
                          side: str, **kwargs) -> bool:
        """入场确认函数 - 仅在 live/dry_run 模式下校验资金费率"""
        runmode = self.config.get('runmode', '')
        if runmode not in ('live', 'dry_run'):
            # 回测时直接放行
            return True
        try:
            ticker = self.dp.ticker(pair)
            if ticker and 'info' in ticker and 'fundingRate' in ticker['info']:
                current_funding_rate = float(ticker['info']['fundingRate'])
                logger.info(f"{pair} - 实时资金费率: {current_funding_rate:.8f}")
                return current_funding_rate >= -0.01
        except Exception as e:
            logger.error(f"{pair} - 获取实时资金费率失败: {e}")
        return False

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                max_leverage: float, entry_tag: Optional[str],
                side: str, **kwargs) -> float:
        """返回交易所支持的最大杠杆，但限制上限为15倍，这样可以使用最少的保证金，而实际使用的杠杆是由position size决定的"""
        return min(max_leverage, self.MAX_LEVERAGE)

    def custom_stake_amount(self, pair: str, current_time: datetime, current_rate: float,
                          proposed_stake: float, min_stake: Optional[float], max_stake: float,
                          leverage: float, entry_tag: Optional[str], side: str,
                          **kwargs) -> float:
        """计算实际下单金额，返回的是保证金金额"""

        # 直接用名义金额/杠杆得到保证金金额
        stake_amount =  self.total_stake / leverage if leverage > 0 else 0        # 保证金金额

        # 确保下单金额在允许范围内
        if min_stake:
            stake_amount = max(min_stake, stake_amount)
        stake_amount = min(max_stake, stake_amount)
        return stake_amount
    
    def custom_stoploss(self, pair: str, trade: Trade, current_time: datetime,
                       current_rate: float, current_profit: float,
                       **kwargs) -> float:
                       
        entry_price = trade.open_rate  # 第一个订单的成交均价
        if current_rate >= entry_price * 1.1:
            return 0.1   
        if current_rate <= entry_price * 0.75:
            return -0.25  
        return None

    def populate_exit_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """生成卖出信号"""
        dataframe['exit_short'] = 0
        return dataframe

    def adjust_trade_position(self, trade: Trade, current_time: datetime,
                            current_rate: float, current_profit: float,
                            min_stake: Optional[float], max_stake: float,
                            current_entry_rate: float, current_exit_rate: float,
                            current_entry_profit: float, current_exit_profit: float,
                            **kwargs) -> Optional[Tuple[float, str]]:
        """动态加仓减仓主函数，协调各个子模块"""
        leverage = trade.leverage                                               # 杠杆
        current_position_ratio = 1.0                                            # 默认仓位比例为100%
        trade_state = trade.get_custom_data('trade_state', {})                  # 从交易中获取状态

        # 追踪止损逻辑
        result, trade_state = self._adjust_trailing_stop(trade, current_profit, leverage, trade_state)
        trade.set_custom_data(key='trade_state', value=trade_state)
        if result:
            return result

        result, trade_state = self._adjust_profit_pullback_exits(trade, current_profit, current_position_ratio, leverage, trade_state)    # 处理浮盈回落减仓逻辑
        trade.set_custom_data(key='trade_state', value=trade_state)             # 处理浮盈回落减仓逻辑
        if result:
            return result

        return None

    def _adjust_trailing_stop(self, trade, current_profit, leverage, trade_state):
        """追踪止损逻辑：从盈利3%*杠杆开始启动，止损比例为3%"""
        
        # 启动阈值：盈利达到3%*杠杆
        start_threshold = 0.15 * leverage
        # 止损比例：3%
        stop_loss_ratio = 0.08 * leverage
        
        # 如果当前盈利未达到启动阈值，不启动追踪止损
        if current_profit < start_threshold:
            return None, trade_state
        
        # 初始化或更新最高盈利记录
        if 'max_profit' not in trade_state:
            trade_state['max_profit'] = current_profit
            trade_state['trailing_stop_activated'] = True
            logger.info(f"交易ID {trade.id} 启动追踪止损，当前盈利: {current_profit:.2%}，杠杆: {leverage}x")
        else:
            # 更新最高盈利
            if current_profit > trade_state['max_profit']:
                trade_state['max_profit'] = current_profit
                logger.info(f"交易ID {trade.id} 更新最高盈利: {current_profit:.2%}，杠杆: {leverage}x")
        
        # 检查是否触发追踪止损
        max_profit = trade_state['max_profit']
        trailing_stop_level = max_profit - stop_loss_ratio
        
        if current_profit <= trailing_stop_level:
            logger.info(f"交易ID {trade.id} 触发追踪止损 - 最高盈利: {max_profit:.2%}, 当前盈利: {current_profit:.2%}, 止损水平: {trailing_stop_level:.2%}, 杠杆: {leverage}x")
            return (-trade.stake_amount, f"追踪止损-杠杆{leverage}x"), trade_state
        
        return None, trade_state

    def _adjust_profit_pullback_exits(self, trade, current_profit, current_position_ratio, leverage, trade_state):
        """处理浮盈回落减仓逻辑，每个档位独立减仓"""

        # 初始化每档减仓标志
        if 'reduced_flags' not in trade_state:
            trade_state['reduced_flags'] = {}

        # 记录达到各档盈利目标
        if current_profit >= 0.08 * leverage:
            if trade_state.get('reduce_position_flag') != "reached_6":
                trade_state['reduce_position_flag'] = "reached_6"
                logger.info(f"交易ID {trade.id} 浮盈6%盈利目标，当前盈利: {current_profit:.2%}")
        

        # 减仓判断
        current_flag = trade_state.get('reduce_position_flag')

        if current_flag == "reached_6" and current_profit <= 0.005 * leverage and not trade_state['reduced_flags'].get('reached_6', False):
            trade_state['reduced_flags']['reached_6'] = True
            return (-trade.stake_amount, "浮盈6%后回落到0.5%全部退出"), trade_state

        return None, trade_state
