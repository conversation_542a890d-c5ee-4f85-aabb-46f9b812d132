{
    "stake_currency": "USDT",
    "fiat_display_currency": "USD",
    "dry_run": true,
    "dry_run_wallet": 1000,
    "tradable_balance_ratio": 1.0,
    "stake_amount": "unlimited",
    "amend_last_stake_amount": false,
    "last_stake_amount_min_ratio": 0.5,
    "position_adjustment_enable": true,
    "cancel_open_orders_on_exit": false,
    "ignore_buying_expired_candle_after": 3000,
    "trading_mode": "futures",
    "margin_mode": "cross", /* 保证金模式：逐仓是isolated，全仓是cross */
    "futures_funding_rate": false,
    "validate_leverage_tiers": false,
    "timezone": "Asia/Shanghai",
    "user_data_dir": "/freqtrade/user_data",
    "db_url": "sqlite:////freqtrade/user_data/tradesv3.sqlite",
    "log": {
        "level": "DEBUG",
        "file": "/freqtrade/user_data/logs/freqtrade.log",
        "rotation": {
            "max_size": "10MB",
            "retention": "7 days"
        }
    },
    "unfilledtimeout": {
        "entry": 10,
        "exit": 10,
        "exit_timeout_count": 0,
        "unit": "minutes"
    },
    "order_types": {
        "entry": "market",
        "exit": "market",
        "emergency_exit": "market",
        "force_entry": "market",
        "force_exit": "market",
        "stoploss": "market",
        "stoploss_on_exchange": true,
        "stoploss_on_exchange_interval": 60
    },
    "entry_pricing": {
        "price_side": "other",
        "use_order_book": true,
        "order_book_top": 1,
        "price_last_balance": 0.0,
        "check_depth_of_market": {
            "enabled": false
        }
    },
    "exit_pricing": {
        "price_side": "other",
        "use_order_book": true,
        "order_book_top": 1
    },
    "exchange": {
        "name": "binance",
        "key": "Z1GQYcHyRJlH44Wds2l15vTi0ZLI49z3yK6a9YwJ18FfLLJb5itMp8lgS6hYYkzR",
        "secret": "wtpdDcF6ePBaHu22DSyHPQUvyQCN5Ln4Lyc11W9rWO8kgOEs9VpnLuUO6YIgWX4e",
        "enable_ws": true,
        "_ft_has_params": {
            "ohlcv_candle_limit": 50
        },
        "ccxt_config": {
            "enableRateLimit": true,
            "defaultType": "future",
            "rateLimit": 50
        },
        "pair_whitelist": [".*USDT:USDT"],
        "pair_blacklist": [
            "USDC/USDT:USDT",
            "BTCDOM/USDT:USDT"
        ]
    },
    "pairlists": [
        {
            "method": "VolumePairList",
            "number_assets": 700,
            "sort_key": "quoteVolume",
            "min_value": 0,
            "refresh_period": 3600
        }
    ],
    "api_server": {
        "enabled": true,
        "listen_ip_address": "0.0.0.0",
        "listen_port": 8080,
        "verbosity": "info",
        "enable_openapi": true,
        "jwt_secret_key": "7xL9#kQ2pZ4!vR8&wE5@tY6*uI1(oP3",
        "ws_token": "sdrfxvbcx34567",
        "CORS_origins": [
            "*"
        ],
        "username": "freqtrade",
        "password": "freqtrade123"
    },
    "bot_name": "Long_Continuous_1H",
    "initial_state": "running",
    "force_entry_enable": false,
    "strategy": "Long_Continuous_1H",
    "internals": {
        "process_throttle_secs": 1,
        "heartbeat_interval": 60,
        "sd_notify": true
    },
    "telegram": {
        "enabled": false,
        "token": "7770581456:AAFePRkB-38gezJwoEzButG_inlj0Cx5Uxg",
        "chat_id": "7593748194"
    }
}